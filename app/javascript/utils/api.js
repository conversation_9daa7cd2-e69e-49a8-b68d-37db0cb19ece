import axios from './axios'

export const getStudents = (params = {}) => {
  return axios.get('/students', { params })
}

export const createObserverLink = (studentId) => {
  return axios.post(`students/${studentId}/create_observer_link`)
}

export const getCurrentObserverLink = () => {
  return axios.get('observer_link/current')
}

export const renewObserverLink = () => {
  return axios.patch('observer_link/renew')
}

export const endObserverLink = () => {
  return axios.delete('observer_link/end')
}

export const expireLink = (observerLinkId) => {
  return axios.patch(`/observer_links/${observerLinkId}/is_expired`)
}
export const student_events = (params = {}) => {
  const { student_id, course_id } = params;
  const queryParams = course_id ? { course_id } : {};
  return axios.get(`/students/${student_id}/student_events`, { params: queryParams });
};

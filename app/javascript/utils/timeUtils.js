import { useState, useEffect } from 'react'

/**
 * Utility functions for time-related operations
 */

/**
 * Calculate the remaining time in seconds from a given expiration date
 * @param {string|Date} expiresAt - The expiration date/time
 * @returns {number} - Remaining seconds (0 if expired)
 */
export const calculateTimeRemaining = (expiresAt) => {
  if (!expiresAt) return 0

  const now = new Date()
  const expirationDate = new Date(expiresAt)
  const remaining = Math.max(0, Math.floor((expirationDate - now) / 1000))

  return remaining
}

/**
 * Format seconds into MM:SS format
 * @param {number} seconds - The number of seconds to format
 * @returns {string} - Formatted time string (MM:SS) or "Expired" if <= 0
 */
export const formatTimeRemaining = (seconds) => {
  if (seconds <= 0) return 'Expired'

  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`
}

/**
 * Get the appropriate status variant based on remaining time
 * @param {number} timeRemaining - Time remaining in seconds
 * @returns {string} - Status variant ('error', 'warning', or 'success')
 */
export const getStatusVariant = (timeRemaining) => {
  if (timeRemaining <= 0) return 'error'
  if (timeRemaining <= 10) return 'warning'
  return 'success'
}

/**
 * Custom hook for managing countdown timer
 * @param {string|Date} expiresAt - The expiration date/time
 * @param {Function} onExpired - Callback function when timer expires
 * @returns {number} - Current time remaining in seconds
 */
export const useCountdownTimer = (expiresAt, onExpired) => {
  const [timeRemaining, setTimeRemaining] = useState(
    calculateTimeRemaining(expiresAt)
  )

  useEffect(() => {
    if (!expiresAt) return

    const timer = setInterval(() => {
      const remaining = calculateTimeRemaining(expiresAt)

      // Check if timer just expired
      if (timeRemaining > 0 && remaining === 0 && onExpired) {
        onExpired()
      }

      setTimeRemaining(remaining)
    }, 1000)

    return () => clearInterval(timer)
  }, [expiresAt, timeRemaining, onExpired])

  return timeRemaining
}

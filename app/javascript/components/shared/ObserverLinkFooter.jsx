import React, { useState } from 'react'
import {
  Flex,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>
} from '@instructure/ui'
import { formatTimeRemaining } from '../../utils/timeUtils'
import StopObservingModal from './StopObservingModal'

const ObserverLinkFooter = ({
  observerLink,
  timeRemaining,
  onEndLink,
  onRenewLink,
  loading = false
}) => {
  const [showStopModal, setShowStopModal] = useState(false)

  if (!observerLink) return null

  const handleConfirmStop = async (student) => {
    await onEndLink(student)
    setShowStopModal(false)
  }

  return (
    <>
     {timeRemaining <= 0 && (
        <Alert variant="info" margin="large 0 0 0" timeout={3000}>
          <Text>
            This observer link has expired. Click "End Observer Link" to return to the student list.
          </Text>
        </Alert>
      )}
      <Flex
        justifyItems="space-between"
        alignItems="center"
        margin="large 0 0 0"
      >
        <Flex.Item>
          Link Status{' '}
          <Text
            color={timeRemaining > 0 ? 'success' : 'danger'}
            weight="bold" transform='capitalize'
          >
            {observerLink.status}
          </Text>
        </Flex.Item>

        <Flex.Item>
          <Flex alignItems="center" gap="medium">
            {/* show timer inline in MM:SS format */}
            <Text weight="bold">
              Time Remaining {formatTimeRemaining(timeRemaining)}
            </Text>

            {observerLink.can_be_renewed && timeRemaining > 0 && (
              <Button
                color="secondary"
                onClick={onRenewLink}
                disabled={loading}
              >
                {loading ? (
                  <Flex alignItems="center" gap="x-small">
                    <Spinner size="x-small" renderTitle="Renewing..." />
                    <Text>Renewing...</Text>
                  </Flex>
                ) : (
                  'Renew Link'
                )}
              </Button>
            )}

            <Button
              color="danger"
              onClick={() => setShowStopModal(true)}
              disabled={loading}
            >
              {loading ? (
                <Flex alignItems="center" gap="x-small">
                  <Spinner size="x-small" renderTitle="Ending link..." />
                  <Text>Ending...</Text>
                </Flex>
              ) : (
                'Stop Observing'
              )}
            </Button>
          </Flex>
        </Flex.Item>
      </Flex>
      <StopObservingModal
        isOpen={showStopModal}
        onClose={() => setShowStopModal(false)}
        student={{
          sortable_name: observerLink.student_name,
          sis_id: observerLink.sis_id,
          timeObserved: observerLink.time_consumed,
          start_at: observerLink.created_at
        }}
        onConfirm={handleConfirmStop}
        loading={loading}
      />
    </>
  )
}

export default ObserverLinkFooter
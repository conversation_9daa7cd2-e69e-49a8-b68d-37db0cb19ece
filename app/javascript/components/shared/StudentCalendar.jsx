import { useEffect } from "react";
import {
  View,
  Flex,
  Text,
  Alert,
} from '@instructure/ui'
import { IconClockLine,IconArrowStartLine,IconArrowEndLine,IconAssignmentLine,IconMoreLine } from '@instructure/ui-icons'
import { formatTimeRemaining, getStatusVariant } from '../../utils/timeUtils'
import { useCountdownTimer } from './CountdownTimer'
import ObserverLinkFooter from './ObserverLinkFooter'
import * as API from './../../utils/api'

const StudentCalendar = ({
  observerLink,
  onEndLink,
  onRenewLink,
  onExpired,
  loading = false
}) => {
  const timeRemaining = useCountdownTimer(observerLink?.expires_at, onExpired)

useEffect(() => {
  async function fetchEvents() {
    try {
      const params = { student_id: observerLink.observed_student.canvas_id };
      const response = await API.student_events(params);
      const data = response.data;
      const eventsArray = data.events || [];
      const formattedEvents = eventsArray.map(event => {
        return {
          id: event.event_id,
          title: event.event_title,
          start: new Date(event.due_at),
          end: new Date(event.due_at),
        };
      });
      setCourses(data.courses || []);
      console.log(data.courses)
      setEvents(formattedEvents);
    } catch (error) {
      console.error("Error fetching events:", error);
    }
  }

  if (observerLink?.observed_student?.canvas_id) {
    fetchEvents();
  }
}, [observerLink]);


  if (!observerLink) return null

  return (
    <View>

      <Alert variant={getStatusVariant(timeRemaining)} margin="0 0 large 0" renderCloseButtonLabel="Close" timeout={5000}>
        <Flex direction="column" gap="small">
          <Text weight="bold">
            {timeRemaining <= 0 ? 'Observer Link Expired' : 'Active Observer Link'}
          </Text>
          <Flex alignItems="center" gap="small">
            <IconClockLine />
            <Text>
              Time remaining: {formatTimeRemaining(timeRemaining)}
            </Text>
          </Flex>
        </Flex>
      </Alert>

      <View
        as="div"
        background="primary"
        padding="medium"
        borderRadius="medium"
        margin="0 0 large 0"
      >
        <Flex direction="column" gap="medium">


          <Flex alignItems="center" gap="small">
            <Flex direction="column" gap="x-small">
              <Text weight="bold">
                {observerLink.observed_student.sortable_name}
              </Text>
              <Text size="small">
                SIS ID: {observerLink.observed_student.sis_id || 'N/A'}
              </Text>
            </Flex>
          </Flex>
        </Flex>
        <Flex >

        </Flex>
      </View>
      <ObserverLinkFooter
        observerLink={observerLink}
        timeRemaining={timeRemaining}
        onEndLink={onEndLink}
        onRenewLink={onRenewLink}
        loading={loading}
      />
    </View>
  )
}

export default StudentCalendar

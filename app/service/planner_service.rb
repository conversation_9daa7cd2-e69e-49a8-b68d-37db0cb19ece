# frozen_string_literal: true

class PlannerService
  def initialize(id)
    @user_id = id
    @start_date = Date.today.beginning_of_month.iso8601
    @end_date   = Date.today.end_of_month.iso8601
    @planner_items_cache = {}
  end

  def planner_items(course_ids: nil)
    ids = Array(course_ids).map(&:to_s).reject(&:blank?).uniq.first(9)
    fetch_items(ids)
  end

  def course_items(course_id)
    return [] unless course_id.present?

    fetch_items([course_id.to_s])
  end

  private

  def fetch_items(course_ids)
    query = {
      as_user_id: @user_id,
      start_date: @start_date,
      end_date: @end_date
    }

    query['context_codes[]'] = course_ids.map { |cid| "course_#{cid}" } if course_ids.present?

    cache_key = query.hash
    @planner_items_cache[cache_key] ||= begin
      url = "/api/v1/planner/items?#{Rack::Utils.build_query(query)}"
      canvas_sync_client.get(url).to_a.uniq
    end
  end
end

# frozen_string_literal: true

class StudentObserverLink < ApplicationRecord
  STATUSES = %w[active expired ended].freeze
  EXPIRATION_DURATION = 1.hour

  belongs_to :observer_user, class_name: 'User', primary_key: :canvas_id, foreign_key: :observer_user_id
  belongs_to :observed_student, class_name: 'User', primary_key: :canvas_id, foreign_key: :observed_student_id

  validates :status, inclusion: { in: STATUSES }
  validates :observer_user_id, presence: true
  validates :observed_student_id, presence: true
  validates :expires_at, presence: true

  # Ensure only one active link per observer at a time
  validates :observer_user_id, uniqueness: {
    scope: :status,
    conditions: -> { where(status: 'active').where('expires_at > ?', Time.current) },
    message: 'can only have one active observer link at a time'
  }

  # Custom validations with detailed error messages
  validate :observer_user_exists
  validate :observed_student_exists
  validate :not_observing_self
  validate :student_is_active

  # Custom validations with detailed error messages
  validate :observer_user_exists
  validate :observed_student_exists
  validate :not_observing_self

  scope :active, -> { where(status: 'active').where('expires_at > ?', Time.current) }
  scope :expired, -> { where(status: 'expired') }
  scope :ended, -> { where(status: 'ended') }
  scope :for_observer, ->(user_id) { where(observer_user_id: user_id) }
  scope :for_student, ->(student_id) { where(observed_student_id: student_id) }
  scope :needs_cleanup, -> { where('expires_at < ? AND status = ?', Time.current, 'active') }

  before_create :set_expiration_time

  after_create :create_observer_link_in_canvas

  def expired?
    expires_at < Time.current
  end

  def can_be_renewed?
    active? && !renewed_at.present?
  end

  def renew!
    return false unless can_be_renewed?

    update!(
      expires_at: expires_at + EXPIRATION_DURATION,
      renewed_at: Time.current
    )
  end

  def active?
    status == 'active' && expires_at > Time.current
  end

  def end_link!
    update!(status: 'ended')
    handle_link_ended_or_expired
  end

  def mark_expired!
    update!(status: 'expired')
  end

  def time_remaining
    return 0 if expired?

    ((expires_at - Time.current) / 1.minute).round
  end

  def time_consumed
    return 0 if expires_at.blank?

    elapsed = (Time.current - (expires_at - EXPIRATION_DURATION)).to_i
    [elapsed, 0].max

    elapsed / 60
  end

  def handle_link_ended_or_expired
    canvas_sync_client.delete("/api/v1/users/#{observer_user_id}/observees/#{observed_student_id}")
    Rails.logger.info('successfully deleted Canvasssss --------------->: ')
  rescue StandardError => e
    Rails.logger.error("Failed to delete observer link in Canvasssss ============>: #{e.message}")
    raise ActiveRecord::Rollback, 'Failed to delete observer link in Canvas'
  end

  private

  def create_observer_link_in_canvas
    canvas_sync_client.put("/api/v1/users/#{observer_user_id}/observees/#{observed_student_id}")
    Rails.logger.info('successfully created Canvasssss --------------->:')
  rescue StandardError => e
    Rails.logger.error("Failed to create observer link in Canvasssss ============>: #{e.message}")
    raise ActiveRecord::Rollback, 'Failed to create observer link in Canvas'
  end

  def set_expiration_time
    self.expires_at = Time.current + EXPIRATION_DURATION
  end

  # Custom validation methods
  def observer_user_exists
    return if observer_user_id.blank?

    return if User.exists?(canvas_id: observer_user_id)

    errors.add(:observer_user_id, 'Observer user not found in the system')
  end

  def observed_student_exists
    return if observed_student_id.blank?

    return if User.exists?(canvas_id: observed_student_id)

    errors.add(:observed_student_id, 'Student not found in the system')
  end

  def not_observing_self
    return if observer_user_id.blank? || observed_student_id.blank?

    return unless observer_user_id == observed_student_id

    errors.add(:observed_student_id, 'You cannot create an observer link to yourself')
  end

  def student_is_active
    return if observed_student_id.blank?

    student = User.find_by(canvas_id: observed_student_id)
    return unless student

    return if student.workflow_state == 'active'

    errors.add(:observed_student_id, 'Student account is not active')
  end
end

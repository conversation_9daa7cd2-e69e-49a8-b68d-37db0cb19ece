# frozen_string_literal: true

json.events @planner_items.each do |event|
  json.partial! 'api/v1/students/event', event: event
end

if @course_items.present?
  @course_items.each do |event|
    json.partial! 'api/v1/students/event', event: event
  end
end

@planner_ids = Set.new(@planner_items.pluck(:course_id).uniq)
if @course_list.present?
  json.courses do
    json.array! @course_list do |course|
      json.partial! 'api/v1/students/courses', course_id: course[0], course_name: course[1], selected_value: @planner_ids.include?(course[0])
    end
  end
end

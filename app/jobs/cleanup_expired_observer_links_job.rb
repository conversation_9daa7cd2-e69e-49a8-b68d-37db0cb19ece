# frozen_string_literal: true

class CleanupExpiredObserverLinksJob < ApplicationJob
  queue_as :default
  BATCH_SIZE = 500

  def perform
    Rails.logger.info 'Starting cleanup of expired observer links'

    User.find_in_batches(batch_size: BATCH_SIZE) do |user_batch|
      Rails.logger.info "Processing batch of #{user_batch.size} users"

      user_batch.each do |user|
        user.against_shards do |shard_user|
          expired_links = StudentObserverLink
                          .where(observer_user_id: shard_user.canvas_id)
                          .needs_cleanup

          if expired_links.any?
            Rails.logger.info "Found #{expired_links.count} expired links for user #{shard_user.canvas_id}"

            expired_links.find_each do |link|
              link.mark_expired!
              Rails.logger.info "Marked link #{link.id} as expired (observer: #{link.observer_user_id}, student: #{link.observed_student_id})"
            rescue StandardError => e
              Rails.logger.error "Failed to mark link #{link.id} as expired: #{e.message}"
            end
          end
        end
      end
    end

    Rails.logger.info 'Completed cleanup of expired observer links'
  end
end

require 'sidekiq/web'
require 'sidekiq-scheduler/web'

Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  if Rails.env.production?
    Sidekiq::Web.use(Rack::Auth::Basic) { |username, password| username == ENV['SIDEKIQ_USERNAME'] && password == ENV['SIDEKIQ_PASSWORD'] }
  end
  mount Sidekiq::Web => '/sidekiq'

  mount PandaPal::Engine, at: '/lti'
  mount CanvasSync::Engine, at: '/canvas_sync'

  root to: 'panda_pal/lti#launch'

  scope '/organizations/:organization_id' do
    lti_nav course_navigation: 'lti#course_navigation'
    lti_nav account_navigation: 'lti#account_navigation'


    namespace :api, defaults: { format: :json } do
      namespace :v1 do
         resources :students, only: [:index] do
          get 'search', on: :collection
          get 'student_events', on: :member
          member do
            post :create_observer_link
          end
         end

         get 'observer_link/current', to: 'observer_links#current'
         patch 'observer_link/renew', to: 'observer_links#renew'
         delete 'observer_link/end', to: 'observer_links#end_link'
         patch 'observer_links/:id/is_expired', to: 'observer_links#is_expired'
      end
    end
  end
end
